// ===== SISTEMA DE CONFIGURAÇÕES DINÂMICAS - ESTÚDIO730 =====
// Versão standalone para compatibilidade total (sem módulos ES6)

(function() {
    'use strict';

    // ===== CONFIGURAÇÕES PADRÃO =====
    function getDefaultConfig() {
        return {
            links: [
                {
                    id: 'whatsapp',
                    name: 'WhatsApp',
                    url: 'https://wa.me/5511999999999?text=Olá! Gostaria de agendar um horário no Estúdio730.',
                    icon: 'fab fa-whatsapp',
                    color: '#25d366',
                    visible: true,
                    removable: false,
                    order: 1
                },
                {
                    id: 'instagram',
                    name: 'Instagram',
                    url: 'https://www.instagram.com/estudio730/',
                    icon: 'fab fa-instagram',
                    color: '#e4405f',
                    visible: true,
                    removable: false,
                    order: 2
                },
                {
                    id: 'location',
                    name: 'Localização',
                    url: 'https://www.google.com/maps/search/?api=1&query=R<PERSON>, 123, São Paulo, SP',
                    icon: 'fas fa-map-marker-alt',
                    color: '#4285f4',
                    visible: true,
                    removable: false,
                    order: 3
                },
                {
                    id: 'website',
                    name: 'Site Oficial',
                    url: 'https://www.estudio730.com.br',
                    icon: 'fas fa-globe',
                    color: '#6c5ce7',
                    visible: true,
                    removable: false,
                    order: 4
                }
            ],
            settings: {
                lastModified: Date.now(),
                version: '1.0.0'
            }
        };
    }

    // ===== UTILITÁRIOS =====

    function isMobile() {
        return window.innerWidth <= 767 ||
               /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    // ===== MAPEAMENTO DE ÍCONES =====
    const iconMap = {
        'fab fa-whatsapp': {
            path: 'M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488',
            defaultColor: '#25d366'
        },
        'fab fa-instagram': {
            path: 'M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z',
            defaultColor: '#e4405f'
        },
        'fas fa-globe': {
            path: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z',
            defaultColor: '#6c5ce7'
        },
        'fas fa-map-marker-alt': {
            path: 'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z',
            defaultColor: '#4285f4'
        },
        'fas fa-link': {
            path: 'M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H6.9C4.01 7 1.9 9.11 1.9 12s2.11 5 5 5h4v-1.9H6.9c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9.1-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.89 0 5-2.11 5-5s-2.11-5-5-5z',
            defaultColor: '#6c5ce7'
        }
    };

    function getIconSVG(iconClass, color) {
        const icon = iconMap[iconClass];
        if (!icon) {
            return `<svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true" style="fill: ${color};">
                <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H6.9C4.01 7 1.9 9.11 1.9 12s2.11 5 5 5h4v-1.9H6.9c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9.1-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.89 0 5-2.11 5-5s-2.11-5-5-5z"/>
            </svg>`;
        }

        const finalColor = color || icon.defaultColor;
        return `<svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true" style="fill: ${finalColor};">
            <path d="${icon.path}"/>
        </svg>`;
    }

    // ===== GERENCIADOR DE TOAST =====
    function showToast(message, type = 'info') {
        const existingToast = document.querySelector('.config-toast');
        if (existingToast) {
            existingToast.remove();
        }

        const toast = document.createElement('div');
        toast.className = `config-toast ${type}`;
        toast.textContent = message;

        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            info: '#3498db',
            warning: '#f39c12'
        };

        toast.style.cssText = `
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: ${colors[type] || colors.info};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            font-weight: 600;
            z-index: 3000;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            animation: slideInRight 0.3s ease-out;
            max-width: 300px;
            font-size: 0.9rem;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => toast.remove(), 300);
        }, 4000);
    }

    // ===== CLASSE PRINCIPAL =====
    class ConfigManager {
        constructor() {
            this.storageKey = 'estudio730_config';
            this.defaultConfig = getDefaultConfig();
            this.currentConfig = this.loadConfig();
            this.init();
        }

        init() {
            this.bindEvents();
            this.renderLinks();
            console.log('🔧 Sistema de Configurações do Estúdio730 inicializado (standalone)');
        }

        loadConfig() {
            try {
                const saved = localStorage.getItem(this.storageKey);
                if (saved) {
                    const config = JSON.parse(saved);
                    return this.mergeConfigs(this.defaultConfig, config);
                }
            } catch (error) {
                console.warn('Erro ao carregar configurações:', error);
            }
            return { ...this.defaultConfig };
        }

        mergeConfigs(defaultConfig, userConfig) {
            const merged = { ...defaultConfig };
            
            if (userConfig.links) {
                const defaultLinks = defaultConfig.links;
                const userLinks = userConfig.links;
                
                merged.links = defaultLinks.map(defaultLink => {
                    const userLink = userLinks.find(ul => ul.id === defaultLink.id);
                    return userLink ? { ...defaultLink, ...userLink } : defaultLink;
                });

                const customLinks = userLinks.filter(ul => !defaultLinks.some(dl => dl.id === ul.id));
                merged.links.push(...customLinks);
            }

            if (userConfig.settings) {
                merged.settings = { ...merged.settings, ...userConfig.settings };
            }

            return merged;
        }

        saveConfig() {
            try {
                this.currentConfig.settings.lastModified = Date.now();
                localStorage.setItem(this.storageKey, JSON.stringify(this.currentConfig));
                this.renderLinks();
                showToast('✅ Configurações salvas com sucesso!', 'success');
                this.closeModal();
            } catch (error) {
                console.error('Erro ao salvar configurações:', error);
                showToast('❌ Erro ao salvar configurações', 'error');
            }
        }

        renderLinks() {
            const linksSection = document.querySelector('.links-section');
            if (!linksSection) return;

            const existingButtons = linksSection.querySelectorAll('.link-button');
            existingButtons.forEach(btn => btn.remove());

            const visibleLinks = this.currentConfig.links
                .filter(link => link.visible)
                .sort((a, b) => a.order - b.order);

            visibleLinks.forEach(link => {
                const linkElement = this.createLinkElement(link);
                linksSection.appendChild(linkElement);
            });
        }

        createLinkElement(link) {
            const linkEl = document.createElement('a');
            linkEl.href = link.url;
            linkEl.className = `link-button custom-link`;
            linkEl.target = '_blank';
            linkEl.rel = 'noopener noreferrer';
            linkEl.style.setProperty('--link-color', link.color);

            const iconSVG = getIconSVG(link.icon, link.color);
            const arrowSVG = `<svg class="icon-svg arrow" viewBox="0 0 24 24" aria-hidden="true">
                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
            </svg>`;

            linkEl.innerHTML = `
                <div class="button-content">
                    ${iconSVG}
                    <span class="button-text">
                        <strong>${link.name}</strong>
                        <small>Clique para acessar</small>
                    </span>
                </div>
                ${arrowSVG}
            `;

            linkEl.addEventListener('mouseenter', () => {
                linkEl.style.borderColor = link.color;
                linkEl.style.boxShadow = `0 15px 40px ${link.color}30`;
            });

            linkEl.addEventListener('mouseleave', () => {
                linkEl.style.borderColor = 'transparent';
                linkEl.style.boxShadow = 'var(--shadow)';
            });

            return linkEl;
        }

        bindEvents() {
            const configButton = document.getElementById('config-button');
            configButton?.addEventListener('click', () => this.openModal());
        }

        openModal() {
            showToast('🔧 Sistema standalone ativo - funcionalidade básica disponível', 'info');
        }

        closeModal() {
            // Implementação básica
        }

        toggleLinkVisibility(linkId) {
            const link = this.currentConfig.links.find(l => l.id === linkId);
            if (link) {
                link.visible = !link.visible;
                this.renderLinks();
                const status = link.visible ? 'visível' : 'oculto';
                showToast(`👁️ Link "${link.name}" agora está ${status}`, 'info');
            }
        }

        moveLink(linkId, direction) {
            showToast('🔧 Funcionalidade disponível na versão completa', 'warning');
        }

        removeLink(linkId) {
            showToast('🔧 Funcionalidade disponível na versão completa', 'warning');
        }

        openEditModal(linkId) {
            showToast('🔧 Funcionalidade disponível na versão completa', 'warning');
        }

        isMobile() {
            return isMobile();
        }
    }

    // ===== INICIALIZAÇÃO =====
    // Adiciona estilos de animação
    const toastStyles = document.createElement('style');
    toastStyles.textContent = `
        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(100px); }
            to { opacity: 1; transform: translateX(0); }
        }
        @keyframes slideOutRight {
            from { opacity: 1; transform: translateX(0); }
            to { opacity: 0; transform: translateX(100px); }
        }
    `;
    document.head.appendChild(toastStyles);

    // Função de inicialização
    function initStandaloneSystem() {
        console.log('🔄 Inicializando sistema standalone...');
        console.log('📍 DOM readyState:', document.readyState);

        try {
            window.configManager = new ConfigManager();
            console.log('✅ ConfigManager criado:', typeof window.configManager);
            console.log('🔧 Métodos disponíveis:', Object.keys(window.configManager));

            // Dispara evento personalizado para notificar que o sistema está pronto
            window.dispatchEvent(new CustomEvent('configSystemReady', {
                detail: { type: 'standalone', manager: window.configManager }
            }));

            console.log('📡 Evento configSystemReady disparado');
            console.log('✅ Sistema standalone inicializado com sucesso');
        } catch (error) {
            console.error('❌ Erro ao inicializar sistema standalone:', error);
        }
    }

    // Inicializa imediatamente se DOM já estiver carregado, senão aguarda
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initStandaloneSystem);
    } else {
        // DOM já carregado - inicializa imediatamente
        initStandaloneSystem();
    }

})();
