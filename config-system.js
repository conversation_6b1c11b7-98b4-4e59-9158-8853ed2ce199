// ===== SISTEMA DE CONFIGURAÇÕES DINÂMICAS - ESTÚDIO730 =====
// Versão refatorada - compatível com execução local e servidor

/**
 * Detecta se está executando em servidor ou localmente
 */
function isRunningOnServer() {
    return window.location.protocol === 'http:' || window.location.protocol === 'https:';
}

/**
 * Carrega o sistema de configurações
 */
async function loadConfigSystem() {
    if (isRunningOnServer()) {
        // Em servidor, usa módulos ES6
        try {
            await import('./config-system/index.js');
            console.log('✅ Sistema de configurações modular carregado com sucesso');
            return true;
        } catch (error) {
            console.error('❌ Erro ao carregar sistema modular:', error);
            return false;
        }
    } else {
        // Localmente, carrega via script tags
        console.log('🔄 Executando localmente - carregando sistema via scripts...');
        return loadSystemViaScripts();
    }
}

/**
 * Carrega sistema via script standalone para compatibilidade local
 */
function loadSystemViaScripts() {
    return new Promise((resolve, reject) => {
        console.log('🔄 Carregando sistema standalone...');

        // Timeout para evitar espera infinita
        const timeout = setTimeout(() => {
            console.warn('⚠️ Timeout ao carregar sistema standalone');
            reject(false);
        }, 10000);

        // Escuta o evento de sistema pronto
        const handleSystemReady = () => {
            clearTimeout(timeout);
            window.removeEventListener('configSystemReady', handleSystemReady);
            console.log('✅ Sistema standalone inicializado e pronto');
            resolve(true);
        };

        window.addEventListener('configSystemReady', handleSystemReady);

        const script = document.createElement('script');
        script.src = 'config-system-standalone.js';
        script.type = 'text/javascript';

        script.onload = () => {
            console.log('📄 Arquivo standalone carregado, aguardando inicialização...');
        };

        script.onerror = () => {
            clearTimeout(timeout);
            window.removeEventListener('configSystemReady', handleSystemReady);
            console.error('❌ Erro ao carregar arquivo standalone');
            reject(false);
        };

        document.head.appendChild(script);
    });
}

/**
 * Sistema de fallback (implementação original simplificada)
 */
function initFallbackSystem() {
    console.log('⚠️ Usando sistema de fallback - funcionalidade limitada');

    // Implementação básica para garantir que a página continue funcionando
    window.configManager = {
        openModal: () => {
            console.log('Modal de configurações não disponível no modo fallback');
            alert('Sistema de configurações em modo limitado. Execute em um servidor web para funcionalidade completa.');
        },
        toggleLinkVisibility: () => console.log('Funcionalidade não disponível no modo fallback'),
        moveLink: () => console.log('Funcionalidade não disponível no modo fallback'),
        removeLink: () => console.log('Funcionalidade não disponível no modo fallback'),
        openEditModal: () => console.log('Funcionalidade não disponível no modo fallback'),
        isMobile: () => window.innerWidth <= 767,
        currentConfig: { links: [] }
    };
}

// Função de inicialização principal
async function initializeConfigSystem() {
    console.log('🚀 Iniciando sistema de configurações...');
    console.log('📍 Protocolo:', window.location.protocol);
    console.log('📍 URL:', window.location.href);

    try {
        const success = await loadConfigSystem();
        if (!success) {
            console.warn('⚠️ Sistema principal falhou, iniciando fallback...');
            initFallbackSystem();
        } else {
            console.log('✅ Sistema de configurações inicializado com sucesso');
        }
    } catch (error) {
        console.error('❌ Erro crítico ao carregar sistema:', error);
        console.log('🔄 Iniciando sistema de fallback...');
        initFallbackSystem();
    }
}

// Inicializa o sistema quando o DOM estiver carregado
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeConfigSystem);
} else {
    // DOM já carregado - inicializa imediatamente
    initializeConfigSystem();
}











