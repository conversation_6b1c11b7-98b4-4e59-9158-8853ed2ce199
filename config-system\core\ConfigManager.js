/**
 * Gerenciador principal de configurações
 * @module ConfigManager
 */

import { ConfigStorage } from './ConfigStorage.js';
import { getDefaultConfig } from './DefaultConfig.js';
import { ToastManager } from '../ui/ToastManager.js';
import { ModalManager } from '../ui/ModalManager.js';
import { FormManager } from '../ui/FormManager.js';
import { TabManager } from '../ui/TabManager.js';
import { LinkManager } from '../links/LinkManager.js';
import { LinkRenderer } from '../links/LinkRenderer.js';
import { MobileOptimizations } from '../effects/MobileOptimizations.js';
import { validateUrlInput } from '../utils/UrlValidator.js';

/**
 * Classe principal para gerenciar configurações
 */
export class ConfigManager {
    constructor() {
        this.storage = new ConfigStorage();
        this.defaultConfig = getDefaultConfig();
        this.currentConfig = this.storage.loadConfig();
        
        // Inicializa gerenciadores
        this.toastManager = new ToastManager();
        this.modalManager = new ModalManager();
        this.formManager = new FormManager();
        this.tabManager = new TabManager();
        this.linkManager = new LinkManager(this.toastManager);
        this.linkRenderer = new LinkRenderer();
        this.mobileOptimizations = new MobileOptimizations();
        
        this.init();
    }

    /**
     * Inicializa o sistema de configurações
     */
    init() {
        this.bindEvents();
        this.linkRenderer.renderMainPageLinks(this.currentConfig.links);
        this.setupRealTimeValidation();
        console.log('🔧 Sistema de Configurações do Estúdio730 inicializado');
    }

    /**
     * Vincula eventos aos elementos da interface
     */
    bindEvents() {
        // Botão de abrir configurações
        const configButton = document.getElementById('config-button');
        configButton?.addEventListener('click', () => this.openModal());

        // Suporte para navegação por teclado no botão de configuração
        configButton?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.openModal();
            }
        });

        // Botão expandir formulário
        const expandButton = document.getElementById('btn-expand-form');
        expandButton?.addEventListener('click', () => this.formManager.toggleAddForm());

        // Botão cancelar formulário
        const cancelFormButton = document.getElementById('btn-cancel-form');
        cancelFormButton?.addEventListener('click', () => this.formManager.hideAddForm());

        // Formulário de adicionar link
        const addForm = document.getElementById('add-link-form');
        addForm?.addEventListener('submit', (e) => this.handleAddLink(e));

        // Formulário de edição
        const editForm = document.getElementById('edit-link-form');
        editForm?.addEventListener('submit', (e) => this.handleEditLink(e));

        // Botões do footer
        document.getElementById('btn-save')?.addEventListener('click', () => this.saveConfig());
        document.getElementById('btn-cancel')?.addEventListener('click', () => this.modalManager.closeModal());
        document.getElementById('btn-restore')?.addEventListener('click', () => this.restoreDefaults());
    }

    /**
     * Abre o modal de configurações
     */
    openModal() {
        this.modalManager.openModal(() => {
            this.linkRenderer.renderConfigLinks(
                this.currentConfig.links,
                () => this.linkRenderer.magicEffectsInitialized = true
            );
        });
    }

    /**
     * Fecha o modal de configurações
     */
    closeModal() {
        this.modalManager.closeModal(() => {
            this.formManager.hideAddForm();
            this.formManager.clearAddForm();
        });
    }

    /**
     * Salva configurações
     */
    saveConfig() {
        const success = this.storage.saveConfig(this.currentConfig);
        if (success) {
            this.linkRenderer.renderMainPageLinks(this.currentConfig.links);
            this.toastManager.success('✅ Configurações salvas com sucesso!');
            this.closeModal();
        } else {
            this.toastManager.error('❌ Erro ao salvar configurações');
        }
    }

    /**
     * Salva configurações silenciosamente
     */
    saveConfigSilently() {
        this.storage.saveConfig(this.currentConfig, true);
    }

    /**
     * Restaura configurações padrão
     */
    restoreDefaults() {
        if (confirm('Tem certeza que deseja restaurar as configurações padrão? Todos os links personalizados serão perdidos.')) {
            this.currentConfig = { ...this.defaultConfig };
            this.saveConfig();
            this.linkRenderer.renderConfigLinks(this.currentConfig.links);
            this.toastManager.info('🔄 Configurações restauradas para o padrão');
        }
    }

    /**
     * Abre modal de edição de link
     */
    openEditModal(linkId) {
        const link = this.linkManager.findLinkById(this.currentConfig.links, linkId);
        if (!link) return;

        this.modalManager.openEditModal(link, (linkData) => {
            this.formManager.fillEditForm(linkData);
        });
    }

    /**
     * Fecha modal de edição
     */
    closeEditModal() {
        this.modalManager.closeEditModal(() => {
            this.formManager.clearEditForm();
        });
    }

    /**
     * Manipula adição de novo link
     */
    handleAddLink(e) {
        e.preventDefault();
        
        const formData = this.formManager.getAddFormData();
        if (!formData) {
            this.toastManager.error('❌ Nome, URL e Ícone são obrigatórios');
            return;
        }

        const result = this.linkManager.addLink(this.currentConfig.links, formData);
        
        if (result.success) {
            this.linkRenderer.renderConfigLinks(this.currentConfig.links);
            this.linkRenderer.renderMainPageLinks(this.currentConfig.links);
            this.formManager.hideAddForm();
            this.toastManager.success(result.message);
        } else {
            this.toastManager.error(result.message);
        }
    }

    /**
     * Manipula edição de link
     */
    handleEditLink(e) {
        e.preventDefault();

        const formData = this.formManager.getEditFormData();
        if (!formData) {
            this.toastManager.error('❌ Nome, URL e Ícone são obrigatórios');
            return;
        }

        const result = this.linkManager.editLink(this.currentConfig.links, formData);
        
        if (result.success) {
            this.linkRenderer.renderConfigLinks(this.currentConfig.links);
            this.closeEditModal();
            this.toastManager.success(result.message);
        } else {
            this.toastManager.error(result.message);
        }
    }

    /**
     * Alterna visibilidade do link
     */
    toggleLinkVisibility(linkId) {
        const result = this.linkManager.toggleLinkVisibility(this.currentConfig.links, linkId);
        
        if (result.success) {
            this.linkRenderer.renderConfigLinks(this.currentConfig.links);
            this.linkRenderer.renderMainPageLinks(this.currentConfig.links);
            this.saveConfigSilently();
            this.toastManager.info(result.message);
        }
    }

    /**
     * Move link para cima ou para baixo
     */
    moveLink(linkId, direction) {
        const result = this.linkManager.moveLink(this.currentConfig.links, linkId, direction);
        
        if (result.success) {
            this.linkRenderer.renderConfigLinks(this.currentConfig.links);
            this.linkRenderer.renderMainPageLinks(this.currentConfig.links);
            this.saveConfigSilently();
            this.toastManager.success(result.message);
        } else {
            this.toastManager.warning(result.message);
        }
    }

    /**
     * Remove link personalizado
     */
    removeLink(linkId) {
        const result = this.linkManager.removeLink(this.currentConfig.links, linkId);
        
        if (result.success) {
            this.linkRenderer.renderConfigLinks(this.currentConfig.links);
            this.toastManager.info(result.message);
        } else if (result.message !== 'Operação cancelada') {
            this.toastManager.error(result.message);
        }
    }

    /**
     * Configura validação em tempo real
     */
    setupRealTimeValidation() {
        this.mobileOptimizations.setupRealTimeValidation(validateUrlInput);
    }

    /**
     * Detecta se é dispositivo móvel
     */
    isMobile() {
        return this.mobileOptimizations.isMobile();
    }
}
