/**
 * Estilos de animação para o sistema de configurações
 * @module animations
 */

/**
 * Inicializa estilos de animação
 */
export function initAnimationStyles() {
    // Verifica se os estilos já foram adicionados
    if (document.getElementById('config-system-animations')) return;

    const animationStyles = document.createElement('style');
    animationStyles.id = 'config-system-animations';
    animationStyles.textContent = `
        /* Animações para toast notifications */
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes slideOutRight {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100px);
            }
        }

        /* Animações para efeitos ripple */
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Animações para modais mobile */
        @keyframes modalSlideUp {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes modalSlideDown {
            from {
                transform: translateY(0);
                opacity: 1;
            }
            to {
                transform: translateY(100%);
                opacity: 0;
            }
        }

        /* Animações para formulários */
        @keyframes formExpand {
            from {
                max-height: 0;
                opacity: 0;
            }
            to {
                max-height: 500px;
                opacity: 1;
            }
        }

        @keyframes formCollapse {
            from {
                max-height: 500px;
                opacity: 1;
            }
            to {
                max-height: 0;
                opacity: 0;
            }
        }

        /* Animações para botões */
        @keyframes buttonPress {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(0.95);
            }
            100% {
                transform: scale(1);
            }
        }

        /* Animações para links */
        @keyframes linkHover {
            from {
                transform: translateY(0);
                box-shadow: var(--shadow);
            }
            to {
                transform: translateY(-2px);
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            }
        }

        /* Animações para loading */
        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        /* Animações para feedback visual */
        @keyframes shake {
            0%, 100% {
                transform: translateX(0);
            }
            10%, 30%, 50%, 70%, 90% {
                transform: translateX(-2px);
            }
            20%, 40%, 60%, 80% {
                transform: translateX(2px);
            }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translateY(0);
            }
            40%, 43% {
                transform: translateY(-8px);
            }
            70% {
                transform: translateY(-4px);
            }
            90% {
                transform: translateY(-2px);
            }
        }

        /* Classes utilitárias para animações */
        .animate-slideInRight {
            animation: slideInRight 0.3s ease-out;
        }

        .animate-slideOutRight {
            animation: slideOutRight 0.3s ease-out;
        }

        .animate-ripple {
            animation: ripple 0.6s ease-out;
        }

        .animate-modalSlideUp {
            animation: modalSlideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .animate-modalSlideDown {
            animation: modalSlideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .animate-formExpand {
            animation: formExpand 0.3s ease-out;
        }

        .animate-formCollapse {
            animation: formCollapse 0.3s ease-out;
        }

        .animate-buttonPress {
            animation: buttonPress 0.2s ease-out;
        }

        .animate-linkHover {
            animation: linkHover 0.3s ease-out;
        }

        .animate-spin {
            animation: spin 1s linear infinite;
        }

        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .animate-shake {
            animation: shake 0.5s ease-in-out;
        }

        .animate-bounce {
            animation: bounce 1s ease-in-out;
        }

        /* Redução de animações para dispositivos com preferência */
        @media (prefers-reduced-motion: reduce) {
            .animate-slideInRight,
            .animate-slideOutRight,
            .animate-ripple,
            .animate-modalSlideUp,
            .animate-modalSlideDown,
            .animate-formExpand,
            .animate-formCollapse,
            .animate-buttonPress,
            .animate-linkHover,
            .animate-spin,
            .animate-pulse,
            .animate-shake,
            .animate-bounce {
                animation: none;
            }
        }

        /* Otimizações para dispositivos móveis */
        @media (max-width: 767px) {
            .reduce-animations .animate-slideInRight,
            .reduce-animations .animate-slideOutRight,
            .reduce-animations .animate-ripple,
            .reduce-animations .animate-linkHover {
                animation-duration: 0.1s;
            }
        }
    `;
    
    document.head.appendChild(animationStyles);
}
