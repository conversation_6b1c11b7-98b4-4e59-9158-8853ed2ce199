/**
 * Gerenciador de links
 * @module LinkManager
 */

import { generateId } from '../utils/IdGenerator.js';
import { isValidUrl } from '../utils/UrlValidator.js';

/**
 * Classe para gerenciar operações CRUD de links
 */
export class LinkManager {
    constructor(toastManager) {
        this.toastManager = toastManager;
    }

    /**
     * Adiciona um novo link
     * @param {Array} links - Array atual de links
     * @param {Object} linkData - Dados do novo link
     * @returns {Object} Resultado da operação
     */
    addLink(links, linkData) {
        const { name, url, icon, color } = linkData;

        // Validação
        if (!name || !url || !icon) {
            return {
                success: false,
                message: '❌ Nome, URL e Ícone são obrigatórios'
            };
        }

        if (!isValidUrl(url)) {
            return {
                success: false,
                message: '❌ URL inválida'
            };
        }

        // Calcula o próximo order baseado no maior valor existente
        const maxOrder = links.length > 0
            ? Math.max(...links.map(l => l.order))
            : 0;

        // Cria novo link
        const newLink = {
            id: generateId(),
            name,
            url,
            icon,
            color,
            visible: true,
            removable: true,
            order: maxOrder + 1
        };

        links.push(newLink);

        return {
            success: true,
            message: `✅ Link "${name}" adicionado com sucesso!`,
            link: newLink
        };
    }

    /**
     * Edita um link existente
     * @param {Array} links - Array atual de links
     * @param {Object} editData - Dados de edição
     * @returns {Object} Resultado da operação
     */
    editLink(links, editData) {
        const { linkId, name, url, icon, color } = editData;

        // Validação
        if (!name || !url || !icon) {
            return {
                success: false,
                message: '❌ Nome, URL e Ícone são obrigatórios'
            };
        }

        if (!isValidUrl(url)) {
            return {
                success: false,
                message: '❌ URL inválida'
            };
        }

        // Encontra e atualiza o link
        const linkIndex = links.findIndex(l => l.id === linkId);
        if (linkIndex === -1) {
            return {
                success: false,
                message: '❌ Link não encontrado'
            };
        }

        // Atualiza o link
        links[linkIndex] = {
            ...links[linkIndex],
            name,
            url,
            icon,
            color
        };

        return {
            success: true,
            message: `✅ Link "${name}" atualizado com sucesso!`,
            link: links[linkIndex]
        };
    }

    /**
     * Remove um link
     * @param {Array} links - Array atual de links
     * @param {string} linkId - ID do link a ser removido
     * @returns {Object} Resultado da operação
     */
    removeLink(links, linkId) {
        const linkIndex = links.findIndex(l => l.id === linkId);
        const link = links[linkIndex];
        
        if (!link || !link.removable) {
            return {
                success: false,
                message: '❌ Este link não pode ser removido'
            };
        }

        if (confirm(`Tem certeza que deseja remover o link "${link.name}"?`)) {
            links.splice(linkIndex, 1);
            return {
                success: true,
                message: `🗑️ Link "${link.name}" removido`,
                removedLink: link
            };
        }

        return {
            success: false,
            message: 'Operação cancelada'
        };
    }

    /**
     * Alterna visibilidade de um link
     * @param {Array} links - Array atual de links
     * @param {string} linkId - ID do link
     * @returns {Object} Resultado da operação
     */
    toggleLinkVisibility(links, linkId) {
        const link = links.find(l => l.id === linkId);
        if (!link) {
            return {
                success: false,
                message: '❌ Link não encontrado'
            };
        }

        link.visible = !link.visible;
        const status = link.visible ? 'visível' : 'oculto';

        return {
            success: true,
            message: `👁️ Link "${link.name}" agora está ${status}`,
            link
        };
    }

    /**
     * Move um link para cima ou para baixo
     * @param {Array} links - Array atual de links
     * @param {string} linkId - ID do link
     * @param {string} direction - Direção ('up' ou 'down')
     * @returns {Object} Resultado da operação
     */
    moveLink(links, linkId, direction) {
        // Ordena os links por order para trabalhar com a ordem correta
        const sortedLinks = [...links].sort((a, b) => a.order - b.order);
        const currentIndex = sortedLinks.findIndex(l => l.id === linkId);

        if (currentIndex === -1) {
            return {
                success: false,
                message: '❌ Link não encontrado'
            };
        }

        // Calcula o novo índice
        const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

        // Validação de limites
        if (newIndex < 0 || newIndex >= sortedLinks.length) {
            const message = direction === 'up'
                ? '❌ Este item já está no topo da lista'
                : '❌ Este item já está no final da lista';
            return {
                success: false,
                message
            };
        }

        // Remove o item da posição atual
        const [movedLink] = sortedLinks.splice(currentIndex, 1);

        // Insere na nova posição
        sortedLinks.splice(newIndex, 0, movedLink);

        // Recalcula todas as ordens sequencialmente
        sortedLinks.forEach((link, index) => {
            link.order = index + 1;
        });

        // Atualiza o array original com as novas ordens
        links.length = 0;
        links.push(...sortedLinks);

        // Feedback visual de sucesso
        const direction_text = direction === 'up' ? 'para cima' : 'para baixo';
        return {
            success: true,
            message: `✅ Link movido ${direction_text}`,
            movedLink
        };
    }

    /**
     * Restaura a ordem padrão dos links
     * @param {Array} links - Array atual de links
     * @param {Array} defaultLinks - Links padrão com ordem original
     * @returns {Object} Resultado da operação
     */
    restoreDefaultOrder(links, defaultLinks) {
        // Atualiza a ordem dos links padrão para a ordem original
        links.forEach(link => {
            const defaultLink = defaultLinks.find(dl => dl.id === link.id);
            if (defaultLink) {
                link.order = defaultLink.order;
            }
        });

        return {
            success: true,
            message: '🔄 Ordem dos links restaurada'
        };
    }

    /**
     * Encontra um link por ID
     * @param {Array} links - Array de links
     * @param {string} linkId - ID do link
     * @returns {Object|null} Link encontrado ou null
     */
    findLinkById(links, linkId) {
        return links.find(l => l.id === linkId) || null;
    }

    /**
     * Obtém links visíveis ordenados
     * @param {Array} links - Array de links
     * @returns {Array} Links visíveis ordenados
     */
    getVisibleLinks(links) {
        return links
            .filter(link => link.visible)
            .sort((a, b) => a.order - b.order);
    }

    /**
     * Obtém todos os links ordenados
     * @param {Array} links - Array de links
     * @returns {Array} Todos os links ordenados
     */
    getAllLinksOrdered(links) {
        return [...links].sort((a, b) => a.order - b.order);
    }
}
