/**
 * Ponto de entrada principal do sistema de configurações
 * @module ConfigSystem
 */

import { ConfigManager } from './core/ConfigManager.js';
import { initAnimationStyles } from './styles/animations.js';

// Inicializa estilos de animação
initAnimationStyles();

// Variável global para compatibilidade
let configManager;

/**
 * Inicializa o sistema de configurações
 */
function initConfigSystem() {
    configManager = new ConfigManager();
    
    // Torna disponível globalmente para compatibilidade com HTML inline
    window.configManager = configManager;
    
    return configManager;
}

// Inicializa quando o DOM estiver carregado
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initConfigSystem);
} else {
    // DOM j<PERSON> carregado
    initConfigSystem();
}

// Exportações para uso como módulo
export { ConfigManager };
export { initConfigSystem };
export default configManager;
