<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste da Refatoração - Sistema de Configurações</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #test-results {
            margin-top: 20px;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Teste da Refatoração do Sistema de Configurações</h1>
    
    <div class="test-section">
        <h2>📋 Informações do Teste</h2>
        <p>Este arquivo testa se a refatoração do <code>config-system.js</code> mantém a compatibilidade e funcionalidade.</p>
        <p><strong>Arquivo original:</strong> 1349 linhas → <strong>Sistema modular:</strong> 18 arquivos organizados</p>
    </div>

    <div class="test-section">
        <h2>🔧 Controles de Teste</h2>
        <button onclick="runAllTests()">Executar Todos os Testes</button>
        <button onclick="testModuleLoading()">Testar Carregamento</button>
        <button onclick="testCompatibility()">Testar Compatibilidade</button>
        <button onclick="testFunctionality()">Testar Funcionalidades</button>
        <button onclick="clearResults()">Limpar Resultados</button>
    </div>

    <div class="test-section">
        <h2>📊 Resultados dos Testes</h2>
        <div id="test-results"></div>
    </div>

    <!-- Elementos necessários para o sistema de configurações -->
    <div style="display: none;">
        <div id="config-button"></div>
        <div id="config-modal"></div>
        <div id="links-list"></div>
        <div class="links-section"></div>
    </div>

    <script>
        let testResults = [];

        function addResult(test, status, message) {
            const result = { test, status, message, timestamp: new Date() };
            testResults.push(result);
            displayResults();
        }

        function displayResults() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(result => `
                <div class="test-result ${result.status}">
                    <strong>${result.test}:</strong> ${result.message}
                    <small style="display: block; margin-top: 5px; opacity: 0.7;">
                        ${result.timestamp.toLocaleTimeString()}
                    </small>
                </div>
            `).join('');
        }

        function clearResults() {
            testResults = [];
            displayResults();
        }

        async function testModuleLoading() {
            addResult('Carregamento de Módulos', 'warning', 'Iniciando teste...');
            
            try {
                // Tenta carregar o sistema modular
                const module = await import('./config-system/index.js');
                
                if (module.ConfigManager) {
                    addResult('Carregamento de Módulos', 'success', 'Sistema modular carregado com sucesso!');
                    return true;
                } else {
                    addResult('Carregamento de Módulos', 'error', 'ConfigManager não encontrado no módulo');
                    return false;
                }
            } catch (error) {
                addResult('Carregamento de Módulos', 'error', `Erro ao carregar: ${error.message}`);
                return false;
            }
        }

        function testCompatibility() {
            addResult('Compatibilidade', 'warning', 'Verificando compatibilidade...');
            
            // Aguarda um pouco para o sistema carregar
            setTimeout(() => {
                const requiredMethods = [
                    'openModal', 'toggleLinkVisibility', 
                    'moveLink', 'removeLink', 'openEditModal'
                ];
                
                let missingMethods = [];
                
                if (window.configManager) {
                    requiredMethods.forEach(method => {
                        if (typeof window.configManager[method] !== 'function') {
                            missingMethods.push(method);
                        }
                    });
                    
                    if (missingMethods.length === 0) {
                        addResult('Compatibilidade', 'success', 'Todos os métodos necessários estão disponíveis');
                    } else {
                        addResult('Compatibilidade', 'error', `Métodos ausentes: ${missingMethods.join(', ')}`);
                    }
                } else {
                    addResult('Compatibilidade', 'error', 'window.configManager não está disponível');
                }
            }, 1000);
        }

        function testFunctionality() {
            addResult('Funcionalidade', 'warning', 'Testando funcionalidades básicas...');
            
            setTimeout(() => {
                if (window.configManager) {
                    try {
                        // Testa se os métodos podem ser chamados sem erro
                        const tests = [
                            () => typeof window.configManager.openModal === 'function',
                            () => typeof window.configManager.isMobile === 'function',
                            () => window.configManager.currentConfig !== undefined,
                            () => Array.isArray(window.configManager.currentConfig?.links)
                        ];
                        
                        const passed = tests.filter(test => {
                            try {
                                return test();
                            } catch (e) {
                                return false;
                            }
                        }).length;
                        
                        if (passed === tests.length) {
                            addResult('Funcionalidade', 'success', `Todos os ${tests.length} testes funcionais passaram`);
                        } else {
                            addResult('Funcionalidade', 'warning', `${passed}/${tests.length} testes passaram`);
                        }
                    } catch (error) {
                        addResult('Funcionalidade', 'error', `Erro nos testes: ${error.message}`);
                    }
                } else {
                    addResult('Funcionalidade', 'error', 'configManager não disponível para teste');
                }
            }, 1500);
        }

        async function runAllTests() {
            clearResults();
            addResult('Teste Completo', 'warning', 'Iniciando bateria completa de testes...');
            
            await testModuleLoading();
            testCompatibility();
            testFunctionality();
            
            setTimeout(() => {
                const successCount = testResults.filter(r => r.status === 'success').length;
                const totalTests = testResults.filter(r => r.test !== 'Teste Completo').length;
                
                if (successCount === totalTests) {
                    addResult('Teste Completo', 'success', `✅ Todos os ${totalTests} testes passaram! Refatoração bem-sucedida.`);
                } else {
                    addResult('Teste Completo', 'warning', `⚠️ ${successCount}/${totalTests} testes passaram. Verifique os erros acima.`);
                }
            }, 2000);
        }

        // Executa teste automático quando a página carrega
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('Inicialização', 'warning', 'Página carregada. Execute os testes para verificar a refatoração.');
            }, 500);
        });
    </script>
    
    <!-- Carrega o sistema de configurações -->
    <script src="config-system.js"></script>
</body>
</html>
