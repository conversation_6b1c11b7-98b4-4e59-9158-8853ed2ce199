<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Simples - Sistema de Configurações</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #console { 
            background: #000; 
            color: #0f0; 
            padding: 10px; 
            border-radius: 4px; 
            font-family: monospace; 
            height: 200px; 
            overflow-y: auto; 
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Teste Simples do Sistema de Configurações</h1>
    
    <div id="status-container">
        <div class="status info">Aguardando carregamento do sistema...</div>
    </div>

    <button onclick="testarSistema()">Testar Sistema</button>
    <button onclick="verificarConfigManager()">Verificar configManager</button>
    <button onclick="limparConsole()">Limpar Console</button>

    <div id="console"></div>

    <!-- Elementos necessários para o sistema -->
    <div style="display: none;">
        <div id="config-button"></div>
        <div class="links-section"></div>
    </div>

    <script>
        let consoleDiv = document.getElementById('console');
        let statusContainer = document.getElementById('status-container');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            consoleDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            statusContainer.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function limparConsole() {
            consoleDiv.innerHTML = '';
        }

        function verificarConfigManager() {
            log('=== VERIFICAÇÃO DO CONFIG MANAGER ===');
            
            if (window.configManager) {
                log('✅ window.configManager existe', 'success');
                log(`📋 Tipo: ${typeof window.configManager}`);
                log(`🔧 Métodos: ${Object.keys(window.configManager).join(', ')}`);
                
                if (window.configManager.currentConfig) {
                    log(`📊 Links: ${window.configManager.currentConfig.links?.length || 0}`);
                }
                
                updateStatus('✅ configManager está disponível e funcionando', 'success');
            } else {
                log('❌ window.configManager NÃO existe', 'error');
                updateStatus('❌ configManager não está disponível', 'error');
            }
        }

        function testarSistema() {
            log('=== TESTE DO SISTEMA ===');
            
            const protocol = window.location.protocol;
            const isServer = protocol === 'http:' || protocol === 'https:';
            
            log(`📍 Protocolo: ${protocol}`);
            log(`🌐 É servidor: ${isServer ? 'Sim' : 'Não'}`);
            log(`📄 DOM readyState: ${document.readyState}`);
            
            verificarConfigManager();
            
            if (window.configManager) {
                try {
                    log('🧪 Testando método isMobile()...');
                    const isMobile = window.configManager.isMobile();
                    log(`📱 isMobile(): ${isMobile}`);
                    
                    log('🧪 Testando método openModal()...');
                    window.configManager.openModal();
                    log('✅ openModal() executado sem erro');
                    
                } catch (error) {
                    log(`❌ Erro ao testar métodos: ${error.message}`, 'error');
                }
            }
        }

        // Escuta eventos do sistema
        window.addEventListener('configSystemReady', (event) => {
            log(`📡 Evento configSystemReady recebido!`);
            log(`📋 Tipo: ${event.detail?.type || 'desconhecido'}`);
            updateStatus('✅ Sistema pronto e inicializado', 'success');
            verificarConfigManager();
        });

        // Log inicial
        window.addEventListener('load', () => {
            log('🚀 Página carregada, aguardando sistema...');
            
            // Verifica periodicamente se o sistema carregou
            let tentativas = 0;
            const maxTentativas = 20;
            
            const verificarPeriodicamente = () => {
                tentativas++;
                
                if (window.configManager) {
                    log(`✅ Sistema detectado após ${tentativas} tentativas`);
                    updateStatus('✅ Sistema carregado com sucesso', 'success');
                    verificarConfigManager();
                } else if (tentativas < maxTentativas) {
                    log(`⏳ Tentativa ${tentativas}/${maxTentativas} - aguardando...`);
                    setTimeout(verificarPeriodicamente, 500);
                } else {
                    log(`❌ Sistema não carregou após ${maxTentativas} tentativas`);
                    updateStatus('❌ Timeout - sistema não carregou', 'error');
                }
            };
            
            setTimeout(verificarPeriodicamente, 1000);
        });

        // Intercepta logs do console para mostrar na página
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            originalLog.apply(console, args);
            log(`LOG: ${args.join(' ')}`);
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            log(`ERROR: ${args.join(' ')}`, 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            log(`WARN: ${args.join(' ')}`, 'warning');
        };
    </script>
    
    <!-- Carrega o sistema de configurações -->
    <script src="config-system.js"></script>
</body>
</html>
